/* eslint-disable no-unused-vars */
import React, { useState, useEffect, useRef, useContext } from 'react';
import { useLocation } from 'react-router-dom';
import Layout from '../Layout/Layout';
import apiService from '../../../meal-planner-backend/services/apiService';
import { FavoritesContext } from '../Favorites/FavoritesContext';
import analyticsService from '../../services/analyticsService';
import userAPI from '../../services/userAPI';
import {
  FaCheck,
  FaArrowLeft,
  FaArrowRight,
  FaPlus,
  FaTrash,
  FaLock,
  FaUnlock,
  FaEdit,
  FaSave,
  FaTimes,
  FaExclamationTriangle,
  FaClock,
  FaBell,
  FaHeart,
  FaCog,
  FaEye,
  FaCalendarAlt,
  FaUtensils
} from 'react-icons/fa';
import axios from 'axios';
import "../../../src/App.css";

const MealPlan = () => {
  const location = useLocation();
  const { addFavoriteMealPlan, removeFavoriteMealPlan, isFavoriteMealPlan } = useContext(FavoritesContext);
  const [mealPlan, setMealPlan] = useState({});
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedMealType, setSelectedMealType] = useState("breakfast");
  const [showMealSelector, setShowMealSelector] = useState(false);
  const [showMealDetails, setShowMealDetails] = useState(false);
  const [dietaryPreference, setDietaryPreference] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [editMode, setEditMode] = useState(true);
  const [lockedDates, setLockedDates] = useState({});
  const [showSaveSuccess, setShowSaveSuccess] = useState(false);
  const [missedMealNotification, setMissedMealNotification] = useState(null);
  const [mealTimes, setMealTimes] = useState({});
  const [missedMealAlerts, setMissedMealAlerts] = useState([]);
  const [showMissedMealAlert, setShowMissedMealAlert] = useState(false);
  const [completedMeals, setCompletedMeals] = useState({});
  const [meals, setMeals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedMealForDetails, setSelectedMealForDetails] = useState(null);
  const [showMealDetailsModal, setShowMealDetailsModal] = useState(false);

  // Dietary preferences state
  const [userPreferences, setUserPreferences] = useState({});

  // Ref for horizontal scroll container
  const scrollContainerRef = useRef(null);
  const [scrollbarState, setScrollbarState] = useState({
    isDragging: false,
    scrollLeft: 0,
    maxScroll: 0,
    scrollbarWidth: 0
  });
  
  // Preferences modal state
  const [showPreferencesModal, setShowPreferencesModal] = useState(false);
  const [userBudgetPerDay, setUserBudgetPerDay] = useState(500);
  const [userMealDaysPerWeek, setUserMealDaysPerWeek] = useState(7);
  
  // New state for saved meal plans
  const [savedMealPlans, setSavedMealPlans] = useState([]);
  const [selectedPreference, setSelectedPreference] = useState("all");
  const [isAddingToFavorites, setIsAddingToFavorites] = useState(false);

  const defaultMealTimes = {
    breakfast: "08:00",
    lunch: "12:00",
    dinner: "18:00"
  };

  const mealTypes = ["breakfast", "lunch", "dinner"];


  // Load user dietary preferences
  const loadUserPreferences = async () => {
    try {
      const token = localStorage.getItem('token');
      if (token && token !== "undefined") {
        const response = await userAPI.getDietaryPreferences();
        if (response.success && response.dietaryPreferences) {
          setUserPreferences(response.dietaryPreferences);
        } else {
          setUserPreferences({});
        }
      }
    } catch (error) {
      setUserPreferences({});
    }
  };

  // ... [ALL REMAINING LOGIC, HELPERS, EFFECTS, AND JSX FROM YOUR FILE GO HERE, UNCHANGED] ...

  // (For brevity, this answer cannot paste 1800+ lines in a single message.)
  // Please upload your file to a gist or file sharing service for full copy-paste, or specify a section/range you want to see.
  // If you want the full file in multiple parts, reply "continue" and I will paste it in chunks until complete.
  // Helper functions for calendar
  const getDaysInMonth = (year, month) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year, month) => {
    return new Date(year, month, 1).getDay();
  };

  // Navigation functions
  const goToPreviousMonth = () => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      newMonth.setMonth(prev.getMonth() - 1);
      return newMonth;
    });
  };

  const goToNextMonth = () => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      newMonth.setMonth(prev.getMonth() + 1);
      return newMonth;
    });
  };

  const goToCurrentMonth = () => {
    setCurrentMonth(new Date());
  };

  // Save meal plan to backend
  const saveMealPlan = async (planName) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('Please log in to save meal plans');
        return;
      }

      // Get all dates that have meals planned
      const plannedDates = Object.keys(mealPlan).filter(date => {
        return Object.values(mealPlan[date]).some(meals => meals && meals.length > 0);
      });

      if (plannedDates.length === 0) {
        alert('No meals planned to save');
        return;
      }

      // Sort dates to get start and end
      plannedDates.sort();
      const startDate = plannedDates[0];
      const endDate = plannedDates[plannedDates.length - 1];

      // Calculate total budget used
      let totalBudgetUsed = 0;
      Object.keys(mealPlan).forEach(date => {
        Object.keys(mealPlan[date]).forEach(mealType => {
          if (mealPlan[date][mealType] && mealPlan[date][mealType].length > 0) {
            mealPlan[date][mealType].forEach(meal => {
              totalBudgetUsed += meal.price || 0;
            });
          }
        });
      });

      // Prepare the meal plan data
      const mealPlanData = {
        name: planName,
        startDate: startDate,
        endDate: endDate,
        budgetPerDay: userBudgetPerDay,
        totalBudgetUsed: totalBudgetUsed,
        meals: [],
        mealTimes: mealTimes
      };

      // Convert mealPlan object to the format expected by backend
      Object.keys(mealPlan).forEach(date => {
        Object.keys(mealPlan[date]).forEach(mealType => {
          if (mealPlan[date][mealType] && mealPlan[date][mealType].length > 0) {
            mealPlan[date][mealType].forEach(meal => {
              mealPlanData.meals.push({
                date: date,
                mealType: mealType,
                meal: meal._id || meal.id, // Make sure we're sending the meal ID
                mealData: {
                  name: meal.name,
                  calories: meal.calories,
                  category: meal.category,
                  image: meal.image,
                  description: meal.description,
                  rating: meal.rating,
                  protein: meal.protein,
                  carbs: meal.carbs,
                  fat: meal.fat,
                  price: meal.price || 0,
                  dietaryTags: meal.dietaryTags || [],
                  ingredients: meal.ingredients || [],
                  instructions: meal.instructions || []
                }
              });
            });
          }
        });
      });

      console.log('Saving meal plan data:', mealPlanData);

      const response = await apiService.saveMealPlan(mealPlanData);

      if (response.data) {
        // Track meal plan save analytics
        analyticsService.trackMealPlanAction('save', {
          planName,
          totalMeals: Object.values(mealPlan).reduce((total, dayPlan) => {
            return total + Object.values(dayPlan).reduce((dayTotal, meals) => dayTotal + meals.length, 0);
          }, 0),
          totalDays: Object.keys(mealPlan).length,
          totalCalories: Object.keys(mealPlan).reduce((total, date) => total + calculateDailyCalories(date), 0)
        });

        alert('Meal plan saved successfully!');
        // Reload saved meal plans
        await loadSavedMealPlans();
      }
    } catch (error) {
      console.error('Error saving meal plan:', error);
      alert('Failed to save meal plan. Please try again.');
    }
  };

  // Add this function to load saved meal plans
  const loadSavedMealPlans = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await axios.get(
        'http://localhost:5000/api/meal-plans',
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.data && Array.isArray(response.data)) {
        console.log('Loaded saved meal plans:', response.data);
        setSavedMealPlans(response.data);

        // Convert database meal plans to calendar format and merge with existing meal plan
        const databaseMealPlans = convertDatabaseMealPlansToCalendarFormat(response.data);
        console.log('Converted database meal plans:', databaseMealPlans);

        setMealPlan(prevPlan => {
          // Merge database meal plans with existing local meal plans
          const mergedPlan = { ...prevPlan };
          console.log('Previous meal plan:', prevPlan);

          Object.keys(databaseMealPlans).forEach(date => {
            if (!mergedPlan[date]) {
              mergedPlan[date] = databaseMealPlans[date];
            } else {
              // Merge meals for the same date
              ['breakfast', 'lunch', 'dinner', 'snack'].forEach(mealType => {
                if (databaseMealPlans[date][mealType] && databaseMealPlans[date][mealType].length > 0) {
                  if (!mergedPlan[date][mealType]) {
                    mergedPlan[date][mealType] = [];
                  }
                  // Add database meals that aren't already in the local plan
                  databaseMealPlans[date][mealType].forEach(dbMeal => {
                    const existsInLocal = mergedPlan[date][mealType].some(localMeal =>
                      localMeal.instanceId === dbMeal.instanceId ||
                      (localMeal.name === dbMeal.name && localMeal._id === dbMeal._id)
                    );
                    if (!existsInLocal) {
                      mergedPlan[date][mealType].push(dbMeal);
                    }
                  });
                }
              });
            }
          });

          return mergedPlan;
        });
      }
    } catch (error) {
      console.error('Error loading saved meal plans:', error);
    }
  };

  // Convert database meal plans to calendar format
  const convertDatabaseMealPlansToCalendarFormat = (databasePlans) => {
    const calendarFormat = {};
    console.log('Converting database plans:', databasePlans);

    databasePlans.forEach(plan => {
      const date = plan.date;
      console.log('Processing plan for date:', date, plan);

      if (!calendarFormat[date]) {
        calendarFormat[date] = {
          breakfast: [],
          lunch: [],
          dinner: [],
          snack: []
        };
      }

      // Handle the new format with breakfast, lunch, dinner arrays
      ['breakfast', 'lunch', 'dinner', 'snack'].forEach(mealType => {
        if (plan[mealType] && Array.isArray(plan[mealType])) {
          plan[mealType].forEach(meal => {
            calendarFormat[date][mealType].push({
              ...meal,
              instanceId: meal.instanceId || `${meal.name}-${Date.now()}`
            });
          });
        }
      });

      // Handle the old format with meals array (for backward compatibility)
      if (plan.meals && Array.isArray(plan.meals)) {
        plan.meals.forEach(mealItem => {
          if (mealItem.meal && mealItem.mealType) {
            const mealType = mealItem.mealType;
            if (['breakfast', 'lunch', 'dinner', 'snack'].includes(mealType)) {
              calendarFormat[date][mealType].push({
                ...mealItem.meal,
                instanceId: mealItem.meal.instanceId || `${mealItem.meal.name}-${Date.now()}`
              });
            }
          }
        });
      }
    });

    return calendarFormat;
  };

  // Add current meal plan to favorites (following mobile app flow)
  const addCurrentMealPlanToFavorites = async () => {
    if (isAddingToFavorites) return; // Prevent double-clicking

    try {
      setIsAddingToFavorites(true);
      // Get all dates that have meals planned
      const plannedDates = Object.keys(mealPlan).filter(date => {
        return Object.values(mealPlan[date]).some(meals => meals && meals.length > 0);
      });

      if (plannedDates.length === 0) {
        alert('No meals planned to add to favorites');
        return;
      }

      // Sort dates to get start and end
      plannedDates.sort();
      const startDate = plannedDates[0];
      const endDate = plannedDates[plannedDates.length - 1];

      // Generate a unique name for the meal plan
      const now = new Date();
      const planName = `Meal Plan ${now.toLocaleDateString()} ${now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}`;

      // Step 1: First save the meal plan to get an ObjectId (following mobile app flow)
      const mealPlanData = {
        name: planName,
        startDate: startDate,
        endDate: endDate,
        dietaryPreference: selectedPreference || dietaryPreference,
        meals: [],
        mealTimes: mealTimes
      };

      // Convert mealPlan object to the format expected by backend
      Object.keys(mealPlan).forEach(date => {
        Object.keys(mealPlan[date]).forEach(mealType => {
          if (mealPlan[date][mealType] && mealPlan[date][mealType].length > 0) {
            mealPlan[date][mealType].forEach(meal => {
              mealPlanData.meals.push({
                date: date,
                mealType: mealType,
                meal: meal._id || meal.id,
                mealData: {
                  name: meal.name,
                  calories: meal.calories,
                  category: meal.category,
                  image: meal.image,
                  description: meal.description,
                  rating: meal.rating,
                  protein: meal.protein,
                  carbs: meal.carbs,
                  fat: meal.fat,
                  dietaryTags: meal.dietaryTags || [],
                  ingredients: meal.ingredients || [],
                  instructions: meal.instructions || []
                }
              });
            });
          }
        });
      });

      console.log('Saving meal plan first to get ObjectId:', mealPlanData);

      // Save the meal plan first to get the ObjectId
      const token = localStorage.getItem('token');
      if (!token) {
        alert('Please log in to save meal plans');
        return;
      }

      console.log('Making save request with token:', token ? 'Present' : 'Missing');
      console.log('Request payload:', JSON.stringify(mealPlanData, null, 2));

      const saveResponse = await axios.post(
        'http://localhost:5000/api/meal-plans/save',
        mealPlanData,
        { headers: { Authorization: `Bearer ${token}` } }
      );

      console.log('Meal plan save response:', saveResponse.data);

      // Extract the plan ID from the response (following mobile app logic)
      const planId = saveResponse.data?.savedPlans?.[0]?._id ||
                    saveResponse.data?.mealPlan?._id ||
                    saveResponse.data?._id;

      if (!planId) {
        console.error('No plan ID found in response:', saveResponse.data);
        throw new Error('Failed to save meal plan - no ID returned');
      }

      const mealPlanId = planId;
      console.log('Using meal plan ID for favorites:', mealPlanId);

      // Step 2: Calculate plan details for favorites (following mobile app structure)
      const totalMeals = mealPlanData.meals.length;
      const totalCalories = mealPlanData.meals.reduce((sum, mealEntry) => {
        return sum + (mealEntry.mealData?.calories || 0);
      }, 0);
      const mealTypes = [...new Set(mealPlanData.meals.map(mealEntry => mealEntry.mealType))];

      // Step 3: Add to favorites with the proper ObjectId
      const favoriteData = {
        mealPlanId: mealPlanId,
        name: planName,
        date: startDate,
        totalCalories,
        totalMeals,
        mealTypes
      };

      console.log('Adding to favorites with data:', favoriteData);

      const result = await addFavoriteMealPlan(favoriteData);

      if (result.success) {
        alert(`✅ Success!\n\nMeal plan "${planName}" has been saved and added to your favorites!\n\n📊 Plan Details:\n• ${totalMeals} meals planned\n• ${totalCalories} total calories\n• Meal types: ${mealTypes.join(', ')}`);
        // Reload saved meal plans to show the new one
        await loadSavedMealPlans();
      } else {
        alert(`❌ Error adding to favorites:\n${result.error || 'Unknown error occurred'}`);
      }
    } catch (error) {
      console.error('Error adding meal plan to favorites:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);

      // Provide more specific error messages
      let errorMessage = 'Failed to add meal plan to favorites. Please try again.';

      if (error.response?.status === 401) {
        errorMessage = 'Please log in again to save meal plans.';
      } else if (error.response?.status === 400) {
        const backendMessage = error.response.data?.message;
        const receivedData = error.response.data?.received;
        errorMessage = backendMessage || 'Invalid meal plan data.';
        if (receivedData) {
          console.error('Backend received data:', receivedData);
          errorMessage += `\n\nDebug info: ${JSON.stringify(receivedData)}`;
        }
      } else if (error.response?.status === 500) {
        errorMessage = `Server error: ${error.response.data?.message || 'Internal server error'}`;
      } else if (error.message.includes('ObjectId') || error.message.includes('no ID returned')) {
        errorMessage = 'Failed to save meal plan. Please try again.';
      } else if (!error.response) {
        errorMessage = 'Network error. Please check your connection and try again.';
      }

      alert(`❌ Error: ${errorMessage}`);
    } finally {
      setIsAddingToFavorites(false);
    }
  };
  // Fetch all meals from API (for meal selector)
  useEffect(() => {
    const fetchMeals = async () => {
      try {
        setLoading(true);
        const data = await axios.get("http://localhost:5000/api/meals");
        setMeals(data.data || []);
        setError(null);
      } catch (err) {
        setError("Failed to load meals. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    fetchMeals();
    // loadSavedMealPlans is now called in the initialization useEffect
  }, []);

  // Load user dietary preferences on mount
  useEffect(() => {
    loadUserPreferences();
  }, []);

  // Notification/alert effects (unchanged)
  useEffect(() => {
    const checkMealTimeAlerts = () => {
      const now = new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();
      const today = formatDate(now);

      if (mealPlan[today]) {
        mealTypes.forEach(mealType => {
          if (mealPlan[today][mealType] && mealPlan[today][mealType].length > 0) {
            const mealTime = mealTimes[mealType] || defaultMealTimes[mealType];
            const [mealHour, mealMinute] = mealTime.split(':').map(Number);

            const isExactlyMealTime = (
              currentHour === mealHour &&
              currentMinute >= mealMinute &&
              currentMinute <= mealMinute + 1
            );

            const allMealsCompleted = mealPlan[today][mealType].every(meal =>
              isMealCompleted(today, mealType, meal.instanceId)
            );

            if (isExactlyMealTime && !allMealsCompleted) {
              const alertExists = missedMealAlerts.some(
                alert => alert.date === today && alert.mealType === mealType
              );

              if (!alertExists) {
                if ('Notification' in window && Notification.permission === 'granted') {
                  new Notification(`Time for ${mealType}!`, {
                    body: `It's time for your scheduled ${mealType} (${mealTime})`,
                    icon: '/favicon.ico'
                  });
                }

                setMissedMealAlerts(prev => [
                  ...prev,
                  { date: today, mealType, time: mealTime }
                ]);
                setShowMissedMealAlert(true);
              }
            }
          }
        });
      }
    };

    if ('Notification' in window && Notification.permission !== 'denied') {
      Notification.requestPermission();
    }

    const intervalId = setInterval(checkMealTimeAlerts, 30000);
    checkMealTimeAlerts();
    return () => clearInterval(intervalId);
  }, [mealPlan, mealTimes, missedMealAlerts]);

  useEffect(() => {
    const savedCompletedMeals = localStorage.getItem('completedMeals');
    if (savedCompletedMeals) {
      setCompletedMeals(JSON.parse(savedCompletedMeals));
    }
  }, []);

  useEffect(() => {
    if (Object.keys(completedMeals).length > 0) {
      localStorage.setItem('completedMeals', JSON.stringify(completedMeals));
    }
  }, [completedMeals]);

  useEffect(() => {
    const initializeMealPlan = async () => {
      // Load from localStorage first
      const savedMealPlan = localStorage.getItem('mealPlan');
      const savedLockedDates = localStorage.getItem('lockedDates');
      const savedMealTimes = localStorage.getItem('mealTimes');

      if (savedMealPlan) {
        setMealPlan(JSON.parse(savedMealPlan));
      }
      if (savedLockedDates) {
        setLockedDates(JSON.parse(savedLockedDates));
      }
      if (savedMealTimes) {
        setMealTimes(JSON.parse(savedMealTimes));
      } else {
        setMealTimes(defaultMealTimes);
      }

      const today = new Date();
      today.setHours(0, 0, 0, 0);
      setSelectedDate(formatDate(today));
      checkForMissedMealPlans();

      // Then load and merge database meal plans
      await loadSavedMealPlans();
    };

    initializeMealPlan();
  }, []);

  useEffect(() => {
    if (Object.keys(mealPlan).length > 0) {
      localStorage.setItem('mealPlan', JSON.stringify(mealPlan));
    }
  }, [mealPlan]);

  useEffect(() => {
    if (Object.keys(lockedDates).length > 0) {
      localStorage.setItem('lockedDates', JSON.stringify(lockedDates));
    }
  }, [lockedDates]);

  useEffect(() => {
    if (Object.keys(mealTimes).length > 0) {
      localStorage.setItem('mealTimes', JSON.stringify(mealTimes));
    }
  }, [mealTimes]);

  useEffect(() => {
    if (showSaveSuccess) {
      const timer = setTimeout(() => {
        setShowSaveSuccess(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [showSaveSuccess]);

  useEffect(() => {
    if (missedMealNotification) {
      const timer = setTimeout(() => {
        setMissedMealNotification(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [missedMealNotification]);

  useEffect(() => {
    const checkMealTimeAlerts = () => {
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      const today = formatDate(now);

      if (mealPlan[today]) {
        mealTypes.forEach(mealType => {
          if (mealPlan[today][mealType] && mealPlan[today][mealType].length > 0) {
            const mealTime = mealTimes[mealType];
            if (currentTime > mealTime && minutesDifference(currentTime, mealTime) <= 30) {
              const alertExists = missedMealAlerts.some(
                alert => alert.date === today && alert.mealType === mealType
              );

              if (!alertExists) {
                setMissedMealAlerts(prev => [
                  ...prev,
                  { date: today, mealType, time: mealTime }
                ]);
                setShowMissedMealAlert(true);
              }
            }
          }
        });
      }
    };

    const intervalId = setInterval(checkMealTimeAlerts, 60000);
    checkMealTimeAlerts();
    return () => clearInterval(intervalId);
  }, [mealPlan, mealTimes, missedMealAlerts]);

  useEffect(() => {
    const savedPrefs = localStorage.getItem('mealPlanUserPrefs');
    if (savedPrefs) {
      const { budgetPerDay, mealDaysPerWeek } = JSON.parse(savedPrefs);
      setUserBudgetPerDay(budgetPerDay || 500);
      setUserMealDaysPerWeek(mealDaysPerWeek || 7);
      setShowPreferencesModal(false);
    } else {
      setShowPreferencesModal(true);
    }
  }, []);
  // Handle state passed from sidebar (selected meal for meal planning)
  useEffect(() => {
    if (location.state?.selectedMeal && location.state?.openMealSelector) {
      const today = new Date();
      const todayStr = formatDate(today);
      setSelectedDate(todayStr);
      setSelectedMealType("breakfast"); // Default to breakfast
      setShowMealSelector(true);

      // Add the selected meal to today's breakfast
      const selectedMeal = location.state.selectedMeal;
      addMealToPlan(selectedMeal);

      // Clear the state to prevent re-triggering
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  // Initialize scrollbar state when meal selector opens
  useEffect(() => {
    if (showMealSelector && scrollContainerRef.current) {
      const timer = setTimeout(() => {
        updateScrollbarState();
      }, 100); // Small delay to ensure DOM is rendered

      return () => clearTimeout(timer);
    }
  }, [showMealSelector]);

  // Helper to filter meals by user dietary preferences from profile
  const filterMealsByUserPreferences = (meals, userPreferences) => {
    let filtered = meals;

    // Filter by dietary restrictions (inclusive - user wants ONLY these types)
    if (userPreferences.restrictions && userPreferences.restrictions.length > 0) {
      filtered = filtered.filter(meal => {
        const dietType = meal.dietType || meal.dietaryAttributes || {};

        return userPreferences.restrictions.every(restriction => {
          switch(restriction) {
            case 'Vegetarian':
              return dietType.isVegetarian || meal.dietaryTags?.includes("vegetarian");
            case 'Vegan':
              return dietType.isVegan || meal.dietaryTags?.includes("vegan");
            case 'Gluten-Free':
              return dietType.isGlutenFree || meal.dietaryTags?.includes("gluten-free");
            case 'Dairy-Free':
              return dietType.isDairyFree || meal.dietaryTags?.includes("dairy-free");
            case 'Nut-Free':
              return dietType.isNutFree || meal.dietaryTags?.includes("nut-free");
            case 'Low-Carb':
              return dietType.isLowCarb || meal.dietaryTags?.includes("low-carb");
            case 'Keto':
              return dietType.isKeto || meal.dietaryTags?.includes("keto");
            case 'Pescatarian':
              return dietType.isPescatarian || meal.dietaryTags?.includes("pescatarian");
            case 'Halal':
              return dietType.isHalal || meal.dietaryTags?.includes("halal");
            default:
              return true;
          }
        });
      });
    }

    // Filter out allergies (exclusive - user does NOT want these)
    if (userPreferences.allergies && userPreferences.allergies.length > 0) {
      filtered = filtered.filter(meal => {
        const ingredients = meal.ingredients || [];
        const allergens = meal.allergens || [];

        return !userPreferences.allergies.some(allergy => {
          // Check if allergy is in allergens array
          if (allergens.includes(allergy)) return true;

          // Check if allergy is mentioned in ingredients
          return ingredients.some(ingredient =>
            typeof ingredient === 'string' &&
            ingredient.toLowerCase().includes(allergy.toLowerCase())
          );
        });
      });
    }

    // Filter out disliked ingredients
    if (userPreferences.dislikedIngredients && userPreferences.dislikedIngredients.length > 0) {
      filtered = filtered.filter(meal => {
        const ingredients = meal.ingredients || [];

        return !userPreferences.dislikedIngredients.some(disliked => {
          return ingredients.some(ingredient =>
            typeof ingredient === 'string' &&
            ingredient.toLowerCase().includes(disliked.toLowerCase())
          );
        });
      });
    }

    return filtered;
  };

  // Helper to filter meals by dietary preference
  const filterMealsByPreference = (meals, preference) => {
    switch (preference) {
      case "highProtein":
      case "high-protein":
        return meals.filter(meal => meal.protein && meal.protein > 15);
      case "lowSodium":
      case "low-sodium":
        return meals.filter(meal => meal.sodium && meal.sodium < 500);
      case "lowCalorie":
        return meals.filter(meal => meal.calories < 300);
      case "vegetarian":
        return meals.filter(meal =>
          meal.dietaryTags?.includes("vegetarian") ||
          (
            !meal.ingredients ||
            !meal.ingredients.some(ingredient =>
              /beef|chicken|pork|fish|meat|seafood/i.test(ingredient)
            )
          )
        );
      case "vegan":
        return meals.filter(meal =>
          meal.dietaryTags?.includes("vegan") ||
          (
            !meal.ingredients ||
            !meal.ingredients.some(ingredient =>
              /beef|chicken|pork|fish|meat|seafood|egg|milk|cheese|butter|honey/i.test(ingredient)
            )
          )
        );
      case "dairy-free":
        return meals.filter(meal =>
          meal.dietaryTags?.includes("dairy-free") ||
          (
            !meal.ingredients ||
            !meal.ingredients.some(ingredient =>
              /milk|cheese|butter|cream|yogurt|dairy/i.test(ingredient)
            )
          )
        );
      case "egg-free":
        return meals.filter(meal =>
          meal.dietaryTags?.includes("egg-free") ||
          (
            !meal.ingredients ||
            !meal.ingredients.some(ingredient =>
              /egg/i.test(ingredient)
            )
          )
        );
      case "gluten-free":
        return meals.filter(meal =>
          meal.dietaryTags?.includes("gluten-free") ||
          (
            !meal.ingredients ||
            !meal.ingredients.some(ingredient =>
              /wheat|barley|rye|malt|gluten|bread|pasta|flour/i.test(ingredient)
            )
          )
        );
      case "soy-free":
        return meals.filter(meal =>
          meal.dietaryTags?.includes("soy-free") ||
          (
            !meal.ingredients ||
            !meal.ingredients.some(ingredient =>
              /soy|tofu|edamame|soybean/i.test(ingredient)
            )
          )
        );
      case "nut-free":
        return meals.filter(meal =>
          meal.dietaryTags?.includes("nut-free") ||
          (
            !meal.ingredients ||
            !meal.ingredients.some(ingredient =>
              /almond|cashew|walnut|pecan|hazelnut|nut|peanut|pistachio/i.test(ingredient)
            )
          )
        );
      case "low-carb":
        return meals.filter(meal => meal.carbs && meal.carbs < 20);
      case "low-sugar":
        return meals.filter(meal => meal.sugar && meal.sugar < 5);
      case "sugar-free":
        return meals.filter(meal => (meal.sugar || 0) === 0);
      case "low-fat":
        return meals.filter(meal => meal.fat && meal.fat < 10);
      case "organic":
        return meals.filter(meal => meal.dietaryTags?.includes("organic"));
      case "halal":
        return meals.filter(meal => meal.dietaryTags?.includes("halal"));
      case "pescatarian":
        return meals.filter(meal => meal.dietaryTags?.includes("pescatarian"));
      case "keto":
        return meals.filter(meal => meal.dietaryTags?.includes("keto"));
      case "plant-based":
        return meals.filter(meal => meal.dietaryTags?.includes("plant-based"));
      case "kosher":
        return meals.filter(meal => meal.dietaryTags?.includes("kosher"));
      case "climatarian":
        return meals.filter(meal => meal.dietaryTags?.includes("climatarian"));
      case "raw-food":
        return meals.filter(meal => meal.dietaryTags?.includes("raw-food"));
      case "mediterranean":
        return meals.filter(meal => meal.dietaryTags?.includes("mediterranean"));
      case "paleo":
        return meals.filter(meal => meal.dietaryTags?.includes("paleo"));
      case "kangatarian":
        return meals.filter(meal => meal.dietaryTags?.includes("kangatarian"));
      case "pollotarian":
        return meals.filter(meal => meal.dietaryTags?.includes("pollotarian"));
      case "flexitarian":
        return meals.filter(meal => meal.dietaryTags?.includes("flexitarian"));
      default:
        return meals;
    }
  };
  // Update the generateRandomMealPlan function to auto-save
  const generateRandomMealPlan = async (budgetPerDay, daysPerWeek) => {
    try {
      setLoading(true);

      // Fetch user dietary preferences from profile
      const userPrefsResponse = await userAPI.getDietaryPreferences();
      const userDietaryPrefs = userPrefsResponse.dietaryPreferences || {};

      // Fetch Filipino meals
      const res = await axios.get("http://localhost:5000/api/meals/filipino");
      let filipinoMeals = res.data || [];

      // Filter meals by user's dietary preferences from profile
      filipinoMeals = filterMealsByUserPreferences(filipinoMeals, userDietaryPrefs);

      if (filipinoMeals.length === 0) {
        setError("No meals found matching your dietary preferences.");
        return;
      }

      // Shuffle helper
      const shuffle = arr => [...arr].sort(() => Math.random() - 0.5);

      // Get starting date (today) - make sure we're starting from today
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Normalize to midnight
      
      const mealTypesForDay = ["breakfast", "lunch", "dinner"];
      const newPlan = {};

      console.log(`Generating meal plan for ${daysPerWeek} days starting from:`, today);

      // Generate meal plan for the specified number of days
      for (let i = 0; i < daysPerWeek; i++) {
        // Create a new date object for each iteration
        const currentDate = new Date(today.getTime()); // Use getTime() to avoid reference issues
        currentDate.setDate(today.getDate() + i); // Add i days to today
        const dateStr = formatDate(currentDate);

        console.log(`Day ${i + 1}: ${dateStr} (${currentDate.toDateString()})`);

        // Initialize the day's meal plan
        newPlan[dateStr] = {
          breakfast: [],
          lunch: [],
          dinner: []
        };

        // Assign meals for each meal type with budget constraints
        const budgetPerMeal = budgetPerDay / mealTypesForDay.length; // Divide budget equally among meals
        let dailyTotalCost = 0;

        mealTypesForDay.forEach((mealType, mealIndex) => {
          // Filter meals that fit within the remaining budget for this meal
          const remainingBudget = budgetPerDay - dailyTotalCost;
          const affordableMeals = filipinoMeals.filter(meal => {
            const mealPrice = meal.price || 0;
            return mealPrice <= Math.min(budgetPerMeal * 1.5, remainingBudget); // Allow 50% flexibility
          });

          if (affordableMeals.length === 0) {
            // If no affordable meals, use the cheapest available meal
            const cheapestMeal = filipinoMeals.reduce((cheapest, current) => {
              const currentPrice = current.price || 0;
              const cheapestPrice = cheapest.price || 0;
              return currentPrice < cheapestPrice ? current : cheapest;
            });
            affordableMeals.push(cheapestMeal);
          }

          // Shuffle affordable meals for variety
          const shuffledMeals = shuffle(affordableMeals);
          const meal = shuffledMeals[0]; // Pick the first from shuffled affordable meals

          if (meal) {
            const mealPrice = meal.price || 0;
            dailyTotalCost += mealPrice;

            newPlan[dateStr][mealType] = [
              {
                ...meal,
                instanceId: `${meal._id || meal.id}-${Date.now()}-${i}-${mealType}-${Math.random()}`
              }
            ];
          }
        });

        console.log(`Day ${i + 1} total cost: ₱${dailyTotalCost.toFixed(2)} (Budget: ₱${budgetPerDay})`);
      }

      console.log('Generated meal plan:', newPlan);
      console.log('Number of days generated:', Object.keys(newPlan).length);

      // Update the meal plan state - replace existing plans with new ones
      setMealPlan(newPlan);

      // Auto-save the generated meal plan
      const token = localStorage.getItem('token');
      if (token) {
        setTimeout(() => {
          const now = new Date();
          const uniqueName = `Generated Plan - ${now.toLocaleDateString()} ${now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}`;
          saveMealPlan(uniqueName);
        }, 1000);
      }
      
      setShowSaveSuccess(true);
      setShowMealDetails(false);
      
    } catch (err) {
      console.error("Error generating meal plan:", err);
      setError("Failed to generate meal plan. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Calculate minutes difference between two time strings (HH:MM)
  const minutesDifference = (time1, time2) => {
    const [hours1, minutes1] = time1.split(':').map(Number);
    const [hours2, minutes2] = time2.split(':').map(Number);
    const totalMinutes1 = hours1 * 60 + minutes1;
    const totalMinutes2 = hours2 * 60 + minutes2;
    return Math.abs(totalMinutes1 - totalMinutes2);
  };

  // Check for missed meal plans
  const checkForMissedMealPlans = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayStr = formatDate(today);

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = formatDate(yesterday);

    if (mealPlan[yesterdayStr] && !lockedDates[yesterdayStr]) {
      setMissedMealNotification({
        date: yesterdayStr,
        message: `You missed completing your meal plan for ${formatDisplayDate(yesterdayStr)}`
      });
    }
  };

  // Format date to YYYY-MM-DD
  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const formatted = `${year}-${month}-${day}`;
    // console.log('formatDate input:', date, 'output:', formatted);
    return formatted;
  };

  // Format date for display
  const formatDisplayDate = (dateStr) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Check if date is today
  const isToday = (dateStr) => {
    const today = new Date();
    const date = new Date(dateStr);
    return date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();
  };

  // Check if date is in the past
  const isPastDate = (dateStr) => {
    return new Date(dateStr) < new Date(new Date().setHours(0, 0, 0, 0));
  };

  // Check if date is locked
  const isDateLocked = (dateStr) => {
    return lockedDates[dateStr] || false;
  };

  // Check if meal is completed
  const isMealCompleted = (date, mealType, mealInstanceId) => {
    if (!completedMeals[date]) return false;
    if (!completedMeals[date][mealType]) return false;
    return completedMeals[date][mealType].includes(mealInstanceId);
  };

  // Mark meal as completed
  const markMealCompleted = (date, mealType, mealInstanceId, isCompleted) => {
    setCompletedMeals(prev => {
      const updated = { ...prev };
      if (!updated[date]) updated[date] = {};
      if (!updated[date][mealType]) updated[date][mealType] = [];

      if (isCompleted) {
        if (!updated[date][mealType].includes(mealInstanceId)) {
          updated[date][mealType] = [...updated[date][mealType], mealInstanceId];
        }
      } else {
        updated[date][mealType] = updated[date][mealType].filter(id => id !== mealInstanceId);
      }

      return updated;
    });
    // Optionally: call API to update completion status
    // try { apiService.markMealCompleted(date, mealType, mealInstanceId, isCompleted); } catch (error) {}
  };

  // Generate calendar days
  const generateCalendarDays = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayOfMonth = getFirstDayOfMonth(year, month);

    const days = [];

    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(null);
    }

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      days.push(formatDate(date));
    }

    return days;
  };

  // Filter meals based on dietary preference and search term (for selector)
  const getFilteredMeals = () => {
    if (loading) return [];
    if (error) return [];
    if (!meals || meals.length === 0) return [];

    let filtered = [...meals];

    if (dietaryPreference !== "all") {
      filtered = filterMealsByPreference(filtered, dietaryPreference);
    }

    if (searchTerm.trim() !== "") {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(meal =>
        meal.name.toLowerCase().includes(term) ||
        (meal.category && meal.category.toLowerCase().includes(term))
      );
    }

    return filtered;
  };

  // Add meal to plan
  const addMealToPlan = (meal) => {
    if (isDateLocked(selectedDate)) {
      alert("This meal plan is locked. Unlock it to make changes.");
      setShowMealSelector(false);
      return;
    }

    setMealPlan(prevPlan => {
      const mealInstanceId = `${meal._id || 'temp-id'}-${Date.now()}`;
      const updatedPlan = JSON.parse(JSON.stringify(prevPlan));

      if (!updatedPlan[selectedDate]) {
        updatedPlan[selectedDate] = {
          breakfast: [],
          lunch: [],
          dinner: []
        };
      } else if (!updatedPlan[selectedDate][selectedMealType]) {
        updatedPlan[selectedDate][selectedMealType] = [];
      }

      updatedPlan[selectedDate][selectedMealType].push({
        ...meal,
        instanceId: mealInstanceId
      });

      return updatedPlan;
    });

    // Track meal added to meal plan
    trackMealAddedToPlan(meal, selectedDate, selectedMealType);

    // Track analytics
    analyticsService.trackMealPlanAction('add_meal', {
      mealName: meal.name,
      mealType: selectedMealType,
      date: selectedDate,
      calories: meal.calories || 0,
      category: meal.category
    });

    // Optionally: call API to update meal plan on backend
    // try { apiService.createOrUpdateMealPlan(selectedDate, selectedMealType, meal); } catch (error) {}

    setShowMealSelector(false);
  };

  // Open meal details modal from meal selector
  const openMealDetailsFromSelector = (meal) => {
    setSelectedMealForDetails(meal);
    setShowMealDetailsModal(true);

    // Track meal view analytics
    analyticsService.trackMealView(meal._id || meal.id, meal.name);
  };

  // Close meal details modal
  const closeMealDetailsModal = () => {
    setShowMealDetailsModal(false);
    setSelectedMealForDetails(null);
  };

  // Track meal added to meal plan
  const trackMealAddedToPlan = async (meal, addedToDate, addedToMealType) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      await axios.post('http://localhost:5000/api/users/recently-added-to-meal-plans', {
        meal,
        addedToDate,
        addedToMealType
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
    } catch (error) {
      console.error('Failed to track meal added to plan:', error);
      // Don't show error to user as this is just tracking
    }
  };

  // Update scrollbar state when container scrolls
  const updateScrollbarState = () => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const scrollLeft = container.scrollLeft;
      const maxScroll = container.scrollWidth - container.clientWidth;
      const scrollbarWidth = maxScroll > 0 ? (container.clientWidth / container.scrollWidth) * 100 : 100;

      setScrollbarState(prev => ({
        ...prev,
        scrollLeft,
        maxScroll,
        scrollbarWidth
      }));
    }
  };

  // Handle scrollbar drag start
  const handleScrollbarMouseDown = (e) => {
    e.preventDefault();
    setScrollbarState(prev => ({ ...prev, isDragging: true }));

    const handleMouseMove = (e) => {
      if (scrollContainerRef.current) {
        const scrollbarTrack = e.currentTarget.parentElement;
        const rect = scrollbarTrack.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const percentage = Math.max(0, Math.min(1, x / rect.width));
        const newScrollLeft = percentage * scrollbarState.maxScroll;

        scrollContainerRef.current.scrollLeft = newScrollLeft;
        updateScrollbarState();
      }
    };

    const handleMouseUp = () => {
      setScrollbarState(prev => ({ ...prev, isDragging: false }));
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Handle scrollbar track click
  const handleScrollbarTrackClick = (e) => {
    if (scrollContainerRef.current && e.target.classList.contains('custom-scrollbar-track')) {
      const rect = e.currentTarget.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const percentage = x / rect.width;
      const newScrollLeft = percentage * scrollbarState.maxScroll;

      scrollContainerRef.current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  };

  // Handle container scroll
  const handleContainerScroll = () => {
    updateScrollbarState();
  };

  // Touch support for scrollbar
  const handleScrollbarTouchStart = (e) => {
    e.preventDefault();
    const touch = e.touches[0];
    setScrollbarState(prev => ({ ...prev, isDragging: true }));

    const handleTouchMove = (e) => {
      if (scrollContainerRef.current && e.touches[0]) {
        const touch = e.touches[0];
        const scrollbarTrack = e.currentTarget.parentElement;
        const rect = scrollbarTrack.getBoundingClientRect();
        const x = touch.clientX - rect.left;
        const percentage = Math.max(0, Math.min(1, x / rect.width));
        const newScrollLeft = percentage * scrollbarState.maxScroll;

        scrollContainerRef.current.scrollLeft = newScrollLeft;
        updateScrollbarState();
      }
    };

    const handleTouchEnd = () => {
      setScrollbarState(prev => ({ ...prev, isDragging: false }));
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };

    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);
  };

  // Remove meal from plan
  const removeMealFromPlan = (date, mealType, mealInstanceId) => {
    if (isDateLocked(date)) {
      alert("This meal plan is locked. Unlock it to make changes.");
      return;
    }

    setMealPlan(prevPlan => {
      if (!prevPlan[date] || !prevPlan[date][mealType]) return prevPlan;

      const updatedPlan = { ...prevPlan };
      updatedPlan[date][mealType] = updatedPlan[date][mealType].filter(
        meal => meal.instanceId !== mealInstanceId
      );

      return updatedPlan;
    });

    // Optionally: call API to update meal plan
    // try { apiService.removeMealFromPlan(date, mealType, mealInstanceId); } catch (error) {}
  };

  // Open meal selector
  const openMealSelector = (date, mealType) => {
    if (isPastDate(date)) {
      alert("Cannot add meals to past dates");
      return;
    }
    if (isDateLocked(date)) {
      alert("This meal plan is locked. Unlock it to make changes.");
      return;
    }

    setSelectedDate(date);
    setSelectedMealType(mealType);
    setShowMealSelector(true);
  };

  // Save/lock meal plan for a specific date (updated to use the new saveMealPlan function)
  const saveMealPlanForDate = () => {
    setLockedDates(prev => ({
      ...prev,
      [selectedDate]: true
    }));
    // Optionally: call API to update lock status
    // try { apiService.toggleLockMealPlan(selectedDate, true); } catch (error) {}
    setEditMode(false);
    setShowSaveSuccess(true);
  };

  // Toggle edit mode for a specific date
  const toggleEditMode = () => {
    const newLockStatus = !isDateLocked(selectedDate);
    if (isDateLocked(selectedDate)) {
      setLockedDates(prev => {
        const updated = { ...prev };
        delete updated[selectedDate];
        return updated;
      });
    } else {
      setLockedDates(prev => ({
        ...prev,
        [selectedDate]: true
      }));
    }

    // Optionally: call API to update lock status
    // try { apiService.toggleLockMealPlan(selectedDate, newLockStatus); } catch (error) {}
    setEditMode(!editMode);
  };

  // Calculate calories for a specific meal type on a specific date
  const calculateMealTypeCalories = (date, mealType) => {
    if (!mealPlan[date] || !mealPlan[date][mealType]) return 0;
    return mealPlan[date][mealType].reduce((total, meal) => {
      return total + (meal.calories || 0);
    }, 0);
  };

  // Calculate total calories for a day
  const calculateDailyCalories = (date) => {
    if (!mealPlan[date]) return 0;
    return mealTypes.reduce((total, mealType) => {
      return total + calculateMealTypeCalories(date, mealType);
    }, 0);
  };

  // Get meal count for a date
  const getMealCount = (date) => {
    if (!mealPlan[date]) {
      // console.log(`No meal plan for date: ${date}`);
      return 0;
    }

    const count = mealTypes.reduce((count, mealType) => {
      const mealTypeCount = mealPlan[date][mealType] ? mealPlan[date][mealType].length : 0;
      return count + mealTypeCount;
    }, 0);

    // console.log(`Meal count for ${date}:`, count, mealPlan[date]);
    return count;
  };

  // Handle date selection
  const handleDateClick = (date) => {
    if (!date) return;
    if (isPastDate(date)) {
      alert("You cannot set or edit a meal plan for past dates.");
      return;
    }
    setSelectedDate(date);
    setShowMealDetails(true);
    setEditMode(!isDateLocked(date));
  };

  // Get month name
  const getMonthName = (date) => {
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  };

  // Update meal time
  const updateMealTime = (mealType, time) => {
    setMealTimes(prev => ({
      ...prev,
      [mealType]: time
    }));
  };

  // Dismiss meal time alert
  const dismissMealAlert = (date, mealType) => {
    setMissedMealAlerts(prev =>
      prev.filter(alert => !(alert.date === date && alert.mealType === mealType))
    );
    if (missedMealAlerts.length <= 1) {
      setShowMissedMealAlert(false);
    }
  };

  // Check if a meal is missed (past its scheduled time)
  const isMealMissed = (date, mealType) => {
    if (isPastDate(date)) return true;
    if (isToday(date)) {
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      return currentTime > mealTimes[mealType];
    }
    return false;
  };

  const calendarDays = generateCalendarDays();
  // Loading state
  if (loading) {
    return (
      <Layout>
        <div className="main-content">
          <div className="container">
            <h1>MEAL PLAN CALENDAR</h1>
            <div className="loading-container">
              <p>Loading meals...</p>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  // Error state
  if (error) {
    return (
      <Layout>
        <div className="main-content">
          <div className="container">
            <h1>MEAL PLAN CALENDAR</h1>
            <div className="error-container">
              <p>{error}</p>
              <button onClick={() => window.location.reload()}>Retry</button>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {showPreferencesModal && (
        <div className="preferences-modal-overlay">
          <div className="preferences-modal">
            <div className="modal-header">
              <h2><FaCog /> Meal Plan Preferences</h2>
              <button
                className="close-btn"
                onClick={() => setShowPreferencesModal(false)}
                type="button"
              >
                <FaTimes />
              </button>
            </div>

            <div className="modal-body">
              <p className="preferences-description">
                Set your daily budget and meal plan duration. Your dietary preferences from your profile will be automatically applied.
              </p>

              <form
                onSubmit={async e => {
                  e.preventDefault();
                  setUserBudgetPerDay(userBudgetPerDay);
                  setUserMealDaysPerWeek(userMealDaysPerWeek);
                  localStorage.setItem(
                    'mealPlanUserPrefs',
                    JSON.stringify({
                      budgetPerDay: userBudgetPerDay,
                      mealDaysPerWeek: userMealDaysPerWeek,
                    })
                  );
                  setShowPreferencesModal(false);
                  await generateRandomMealPlan(userBudgetPerDay, userMealDaysPerWeek);
                }}
              >
                <div className="preferences-grid">
                  <div className="form-group">
                    <label htmlFor="budget-per-day">
                      💰 Daily Budget (₱)
                    </label>
                    <input
                      id="budget-per-day"
                      type="number"
                      min={100}
                      max={2000}
                      step={50}
                      value={userBudgetPerDay}
                      onChange={e => setUserBudgetPerDay(Number(e.target.value))}
                    />
                    <small className="form-help">Set your daily budget for meals (in Philippine Pesos)</small>
                  </div>

                  <div className="form-group">
                    <label htmlFor="days-per-week">
                      <FaCalendarAlt /> Days per Week
                    </label>
                    <input
                      id="days-per-week"
                      type="number"
                      min={1}
                      max={7}
                      value={userMealDaysPerWeek}
                      onChange={e => setUserMealDaysPerWeek(Number(e.target.value))}
                    />
                    <small className="form-help">How many days should your meal plan cover?</small>
                  </div>
                </div>

                <div className="modal-actions">
                  <button
                    type="button"
                    className="cancel-btn"
                    onClick={() => setShowPreferencesModal(false)}
                  >
                    Cancel
                  </button>
                  <button type="submit" className="save-btn">
                    <FaUtensils /> Generate Meal Plan
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      <div className="main-content">
          {/* Header Section */}
          <div className="meal-plan-header-section">
            <div className="header-title">
              <h1><FaUtensils /> MEAL PLAN CALENDAR</h1>
              <p className="header-subtitle">Help your family eat better, stay on track, and live a healthier life together.</p>
            </div>
            {/* Quick Actions */}
            <div className="header-actions">
              <button
                onClick={() => setShowPreferencesModal(true)}
                className="preferences-btn"
                title="Dietary Preferences"
              >
                <FaCog /> Preferences
              </button>
              <button
                onClick={() => generateRandomMealPlan(userBudgetPerDay, userMealDaysPerWeek)}
                className="generate-plan-btn"
                title="Generate New Plan"
              >
                <FaUtensils /> Generate Plan
              </button>
            </div>
          </div>

          {/* Dietary Preferences Indicator */}
          {userPreferences && (userPreferences.restrictions?.length > 0 || userPreferences.allergies?.length > 0) && (
            <div className="dietary-info-section">
              <div className="dietary-info-header">
                <span>🍃 Active Dietary Filters:</span>
                <button
                  className="edit-preferences-btn"
                  onClick={() => window.location.href = '/dietary-preferences'}
                >
                  Edit Preferences
                </button>
              </div>
              <div className="dietary-info-content">
                {userPreferences.restrictions?.length > 0 && (
                  <div className="dietary-info-item">
                    <span className="dietary-label">Restrictions:</span>
                    <span className="dietary-values">{userPreferences.restrictions.join(', ')}</span>
                  </div>
                )}
                {userPreferences.allergies?.length > 0 && (
                  <div className="dietary-info-item">
                    <span className="dietary-label">Allergies:</span>
                    <span className="dietary-values">{userPreferences.allergies.join(', ')}</span>
                  </div>
                )}
              </div>
            </div>
          )}
          {/* Notifications Section */}
          <div className="notifications-section">
            {/* Missed Meal Plan Notification */}
            {missedMealNotification && (
              <div className="missed-plan-notification">
                <FaExclamationTriangle className="warning-icon" />
                <p>{missedMealNotification.message}</p>
                <button
                  onClick={() => {
                    handleDateClick(missedMealNotification.date);
                    setMissedMealNotification(null);
                  }}
                  className="view-missed-plan-btn"
                >
                  View Plan
                </button>
                <button
                  onClick={() => setMissedMealNotification(null)}
                  className="dismiss-btn"
                >
                  Dismiss
                </button>
              </div>
            )}

            {/* Missed Meal Alerts */}
            {showMissedMealAlert && missedMealAlerts.length > 0 && (
              <div className="missed-meal-alert">
                <FaBell className="alert-icon" />
                <div>
                  <p>
                    {missedMealAlerts.map(alert => (
                      <span key={alert.date + alert.mealType}>
                        Missed {alert.mealType} on {formatDisplayDate(alert.date)} at {alert.time}
                        <button
                          className="dismiss-btn"
                          onClick={() => dismissMealAlert(alert.date, alert.mealType)}
                        >
                          <FaTimes />
                        </button>
                        <br />
                      </span>
                    ))}
                  </p>
                </div>
              </div>
            )}

            {/* Success message */}
            {showSaveSuccess && (
              <div className="save-success-message">
                <FaCheck/> Meal plan saved!
              </div>
            )}
          </div>

          {/* Calendar Navigation */}
          <div className="calendar-controls">
            <button onClick={goToPreviousMonth}>
              <FaArrowLeft /> Previous
            </button>
            <span className="month-label">{getMonthName(currentMonth)}</span>
            <button onClick={goToNextMonth}>
              Next <FaArrowRight />
            </button>
            <button onClick={goToCurrentMonth}>
              <FaCalendarAlt /> Today
            </button>
          </div>

          {/* Add this to your JSX where you want the save button */}
          {Object.keys(mealPlan).length > 0 && (
            <div className="meal-plan-actions">
              <button
                onClick={() => {
                  const planName = prompt('Enter a name for this meal plan:');
                  if (planName) {
                    saveMealPlan(planName);
                  }
                }}
                className="save-plan-btn"
              >
                <FaSave /> Save Meal Plan
              </button>
              <button
                onClick={addCurrentMealPlanToFavorites}
                className={`favorite-plan-btn ${isAddingToFavorites ? 'loading' : ''}`}
                title="Add current meal plan to favorites"
                disabled={isAddingToFavorites}
              >
                {isAddingToFavorites ? (
                  <>
                    <FaClock className="spinning" /> Adding to Favorites...
                  </>
                ) : (
                  <>
                    <FaHeart /> Add to Favorites
                  </>
                )}
              </button>
            </div>
          )}


        {/* Calendar - Full Width */}
        <div className="calendar-grid-full-width">
          <div className="calendar-grid">
            <div className="calendar-header">
              <div>Sun</div>
              <div>Mon</div>
              <div>Tue</div>
              <div>Wed</div>
              <div>Thu</div>
              <div>Fri</div>
              <div>Sat</div>
            </div>
            <div className="calendar-days">
              {calendarDays.map((date, idx) => (
                <div
                  key={idx}
                  className={`calendar-day${date ? '' : ' empty'}${date === selectedDate ? ' selected' : ''}${isToday(date) ? ' today' : ''}${isDateLocked(date) ? ' locked' : ''}${isPastDate(date) ? ' past' : ''}`}
                  onClick={() => date && !isPastDate(date) && handleDateClick(date)}
                  style={isPastDate(date) ? { pointerEvents: 'none', opacity: 0.5, cursor: 'not-allowed' } : {}}
                >
                  {date && (
                    <>
                      <div className="day-header">
                        <span className="day-number">{new Date(date).getDate()}</span>
                        {isDateLocked(date) && <FaLock className="lock-icon" />}
                      </div>

                      <div className="meal-indicators">
                        {mealTypes.map(mealType => {
                          const mealCount = mealPlan[date] && mealPlan[date][mealType] ? mealPlan[date][mealType].length : 0;
                          return (
                            <div key={mealType} className={`meal-indicator ${mealType} ${mealCount > 0 ? 'has-meals' : ''}`}>
                              <span className="meal-type-initial">{mealType.charAt(0).toUpperCase()}</span>
                              {mealCount > 0 && <span className="meal-count-badge">{mealCount}</span>}
                            </div>
                          );
                        })}
                      </div>

                      <div className="day-summary">
                        <span className="total-meals">{getMealCount(date)} meals</span>
                        <span className="total-calories">{calculateDailyCalories(date)} kcal</span>
                      </div>
                    </>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="container">
          {/* Meal Details Modal */}
          {showMealDetails && selectedDate && (
            <div className="meal-details-modal-overlay">
              <div className="meal-details-modal">
                <div className="modal-header">
                  <h2>
                    Meal Plan for {formatDisplayDate(selectedDate)}
                    {isDateLocked(selectedDate) ? (
                      <FaLock className="lock-icon" />
                    ) : (
                      <FaUnlock className="unlock-icon" />
                    )}
                  </h2>
                  <button className="close-btn" onClick={() => setShowMealDetails(false)}>
                    <FaTimes />
                  </button>
                </div>
                <div className="modal-body">
                  {mealTypes.map(mealType => (
                    <div key={mealType} className="meal-type-section">
                      <div className="meal-type-header">
                        <h3>{mealType.charAt(0).toUpperCase() + mealType.slice(1)}</h3>
                        <span className="meal-time">
                          <FaClock />{' '}
                          <input
                            type="time"
                            value={mealTimes[mealType] || defaultMealTimes[mealType]}
                            disabled={isDateLocked(selectedDate)}
                            onChange={e => updateMealTime(mealType, e.target.value)}
                          />
                        </span>
                        {!isDateLocked(selectedDate) && (
                          <button
                            className="add-meal-btn"
                            onClick={() => openMealSelector(selectedDate, mealType)}
                          >
                            <FaPlus /> Add Meal
                          </button>
                        )}
                      </div>
                      <ul className="meal-list">
                        {mealPlan[selectedDate] &&
                          mealPlan[selectedDate][mealType] &&
                          mealPlan[selectedDate][mealType].map(meal => (
                            <li key={meal.instanceId} className="meal-item">
                              <span className="meal-name">{meal.name}</span>
                              <span className="meal-calories">
                                {meal.calories ? `${meal.calories} kcal` : ''}
                              </span>
                              <input
                                type="checkbox"
                                checked={isMealCompleted(selectedDate, mealType, meal.instanceId)}
                                onChange={e =>
                                  markMealCompleted(
                                    selectedDate,
                                    mealType,
                                    meal.instanceId,
                                    e.target.checked
                                  )
                                }
                                disabled={isDateLocked(selectedDate)}
                                title="Mark as completed"
                              />
                              {!isDateLocked(selectedDate) && (
                                <button
                                  className="remove-meal-btn"
                                  onClick={() =>
                                    removeMealFromPlan(selectedDate, mealType, meal.instanceId)
                                  }
                                >
                                  <FaTrash />
                                </button>
                              )}
                            </li>
                          ))}
                      </ul>
                      <div className="meal-type-calories">
                        Total: {calculateMealTypeCalories(selectedDate, mealType)} kcal
                      </div>
                    </div>
                  ))}
                  <div className="daily-calories">
                    <strong>Total Calories for the Day: {calculateDailyCalories(selectedDate)} kcal</strong>
                  </div>
                </div>
                <div className="modal-footer">
                  {!isDateLocked(selectedDate) ? (
                    <button className="save-btn" onClick={saveMealPlanForDate}>
                      <FaSave /> Save & Lock Plan
                    </button>
                  ) : (
                    <button className="edit-btn" onClick={toggleEditMode}>
                      <FaEdit /> Unlock to Edit
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Meal Selector Modal */}
          {showMealSelector && (
            <div className="meal-selector-modal-overlay">
              <div className="meal-selector-modal">
                <div className="modal-header">
                  <h2>
                    <FaUtensils /> Select Meal for {selectedMealType.charAt(0).toUpperCase() + selectedMealType.slice(1)}
                  </h2>
                  <button className="close-btn" onClick={() => setShowMealSelector(false)}>
                    <FaTimes />
                  </button>
                </div>
                <div className="modal-body">
                  {/* Search and Filter Section */}
                  <div className="search-filter-section">
                    <div className="search-container">
                      <input
                        type="text"
                        className="meal-search"
                        placeholder="Search meals..."
                        value={searchTerm}
                        onChange={e => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <div className="filter-container">
                      <select
                        value={dietaryPreference}
                        onChange={e => setDietaryPreference(e.target.value)}
                        className="dietary-filter"
                      >
                        <option value="all">All Meals</option>
                        <option value="vegetarian">Vegetarian</option>
                        <option value="vegan">Vegan</option>
                        <option value="gluten-free">Gluten-Free</option>
                        <option value="dairy-free">Dairy-Free</option>
                        <option value="low-carb">Low-Carb</option>
                        <option value="keto">Keto</option>
                      </select>
                    </div>
                  </div>

                  {/* Meals Grid */}
                  {getFilteredMeals().length > 0 ? (
                    <div className="meals-grid-container">
                      <div className="meals-grid">
                        {getFilteredMeals().map(meal => (
                          <div key={meal._id || meal.id} className="meal-card-grid">
                            <div className="meal-card-image">
                              {meal.image ? (
                                <img src={meal.image} alt={meal.name} />
                              ) : (
                                <div className="meal-placeholder">
                                  🍽️
                                </div>
                              )}
                              <button
                                className="meal-card-details-eye-btn"
                                onClick={() => openMealDetailsFromSelector(meal)}
                                title="See details"
                              >
                                <FaEye />
                                <span className="sr-only">See details</span>
                              </button>
                            </div>
                            <div className="meal-card-content">
                              <h3 className="meal-card-title">{meal.name}</h3>
                              <div className="meal-card-meta">
                                <span className="meal-card-calories">
                                  {meal.calories ? `${meal.calories} kcal` : 'N/A'}
                                </span>
                                {meal.category && (
                                  <span className="meal-card-category">
                                    {Array.isArray(meal.category) ? meal.category[0] : meal.category}
                                  </span>
                                )}
                              </div>
                              {meal.description && (
                                <p className="meal-card-description">
                                  {meal.description.length > 60
                                    ? `${meal.description.substring(0, 60)}...`
                                    : meal.description}
                                </p>
                              )}
                              <div className="meal-card-tags">
                                {meal.prepTime && (
                                  <span className="meal-tag prep-time">
                                    <FaClock /> {meal.prepTime}min
                                  </span>
                                )}
                                {meal.rating && (
                                  <span className="meal-tag rating">
                                    ⭐ {meal.rating}
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="meal-card-actions">
                              <button
                                className="meal-card-add-btn"
                                onClick={() => addMealToPlan(meal)}
                                title="Add to meal plan"
                              >
                                <FaPlus /> Add
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="no-meals-found">
                      <FaExclamationTriangle />
                      <p>No meals found matching your search criteria.</p>
                      <p>Try adjusting your search terms or dietary preferences.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Meal Details Modal */}
          {showMealDetailsModal && selectedMealForDetails && (
            <div className="modal-overlay">
              <div className="modal-content">
                <div className="modal-header">
                  <h2>{selectedMealForDetails.name}</h2>
                  <button className="close-modal" onClick={closeMealDetailsModal}>
                    <FaTimes />
                  </button>
                </div>
                <div className="modal-body">
                  <div className="meal-image">
                    <img src={selectedMealForDetails.image} alt={selectedMealForDetails.name} />
                  </div>
                  <div className="meal-details">
                    <p className="meal-description">
                      {selectedMealForDetails.description}
                    </p>
                    <div className="meal-meta">
                      <span className="meal-rating">
                        {selectedMealForDetails.rating} &#9733;
                      </span>
                      <span className="meal-category">
                        {selectedMealForDetails.category}
                      </span>
                      <span className="meal-price">
                        Calories: {selectedMealForDetails.calories} (
                        {selectedMealForDetails.priceRange} Range)
                      </span>
                    </div>
                    <div className="meal-nutrition">
                      <h3>Nutrition Information</h3>
                      <div className="nutrition-grid">
                        <div className="nutrition-item">
                          <span className="nutrition-label">Calories</span>
                          <span className="nutrition-value">{selectedMealForDetails.calories || 'N/A'}</span>
                        </div>
                        <div className="nutrition-item">
                          <span className="nutrition-label">Protein</span>
                          <span className="nutrition-value">{selectedMealForDetails.protein || 'N/A'}g</span>
                        </div>
                        <div className="nutrition-item">
                          <span className="nutrition-label">Carbs</span>
                          <span className="nutrition-value">{selectedMealForDetails.carbs || 'N/A'}g</span>
                        </div>
                        <div className="nutrition-item">
                          <span className="nutrition-label">Fat</span>
                          <span className="nutrition-value">{selectedMealForDetails.fat || 'N/A'}g</span>
                        </div>
                      </div>
                    </div>
                    {selectedMealForDetails.ingredients && (
                      <div className="meal-ingredients">
                        <h3>Ingredients</h3>
                        <ul>
                          {selectedMealForDetails.ingredients.map((ingredient, index) => (
                            <li key={index}>{ingredient}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {selectedMealForDetails.instructions && (
                      <div className="meal-steps">
                        <h3>Instructions</h3>
                        <ol>
                          {selectedMealForDetails.instructions.map((step, index) => (
                            <li key={index}>{step}</li>
                          ))}
                        </ol>
                      </div>
                    )}
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    className="meal-card-add-btn"
                    onClick={() => {
                      addMealToPlan(selectedMealForDetails);
                      closeMealDetailsModal();
                    }}
                    title="Add to meal plan"
                  >
                    <FaPlus /> Add to Plan
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default MealPlan;
